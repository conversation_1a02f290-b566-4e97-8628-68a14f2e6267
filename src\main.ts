import { initFireBase, db } from "./firestart";
import { Client } from "./models/client";
import { Inspection } from "./models/inspection";
import {
  deleteCompanyDataFromFirebase,
  importC005Data,
} from "./data-imports-c005";

initFireBase();

// **************************************** //

let companyId = "C005";
let insps: Inspection[] = [];
let clients: Client[] = [];

async function getCompanyName(companyId: string): Promise<string> {
  let res = await db.collection("companies").doc(companyId).get();
  return res.data()!.name;
}

async function fixClientsLastDistributionDate(companyId: string) {
  console.log(
    "Fixing clients last distribution date for company",
    companyId,
    await getCompanyName(companyId)
  );

  // Get all clients for the company
  const clientsSnapshot = await db
    .collection("clients")
    .where("companyId", "==", companyId)
    .get();

  console.log(`Found ${clientsSnapshot.docs.length} clients to process`);

  // Process each client
  for (let i = 0; i < clientsSnapshot.docs.length; i++) {
    const clientDoc = clientsSnapshot.docs[i];
    const client = clientDoc.data() as Client;

    console.log(
      `Processing client ${i + 1}/${clientsSnapshot.docs.length}: ${
        client.fullName
      }`
    );

    try {
      // Find the latest distribution for this client
      const distributionsSnapshot = await db
        .collection("distributions")
        .where("clientId", "==", clientDoc.id)
        .where("portableEndpoint", "==", false)
        .orderBy("distributionDate", "desc")
        .limit(1)
        .get();

      let lastDistributionDate = "";

      if (!distributionsSnapshot.empty) {
        const latestDistribution = distributionsSnapshot.docs[0].data();
        const distributionDate = latestDistribution.distributionDate;

        // Format the date to yyyy-mm-dd if it's not already in that format
        if (distributionDate) {
          lastDistributionDate = formatDateToYYYYMMDD(distributionDate);
        }
      }

      // Update the client's lastDistributionDate if it's different
      if (client.lastDistributionDate !== lastDistributionDate) {
        await clientDoc.ref.update({
          lastDistributionDate: lastDistributionDate,
        });

        console.log(
          `  Updated ${client.fullName}: ${client.lastDistributionDate} -> ${lastDistributionDate}`
        );
      } else {
        console.log(
          `  No change needed for ${client.fullName}: ${lastDistributionDate}`
        );
      }
    } catch (error) {
      console.error(`  Error processing client ${client.fullName}:`, error);
    }
  }

  console.log("Finished processing all clients");
}

// Helper function to format date to yyyy-mm-dd
function formatDateToYYYYMMDD(dateString: string): string {
  if (!dateString) return "";

  // If already in yyyy-mm-dd format, return as is
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }

  // Try to parse the date and format it
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      console.warn(`Invalid date format: ${dateString}`);
      return dateString; // Return original if can't parse
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.warn(`Error formatting date ${dateString}:`, error);
    return dateString; // Return original if error
  }
}

async function main() {
  // await deleteCompanyDataFromFirebase("C005");
  await importC005Data();
  //
}

main().catch((error) => console.error("Error:", error));
