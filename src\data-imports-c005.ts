import * as XLSX from "xlsx";
import mysql from "mysql2/promise";
import { Client } from "./models/client";
import { Inspection } from "./models/inspection";
import { UUID } from "uuidv4";

export async function readAndInsertExcelToMySQL(filePath: string) {
  // Extract filename without path and extension
  const fileName = filePath.split('\\').pop()?.split('.')[0] || '';
  
  // Hebrew to English column mapping
  const columnMapping: { [key: string]: string } = {
    "שם לקוח": "client_name",
    "מספר לקוח": "client_number",
    "תאריך בדיקה": "inspection_date",
    "תאריך בדיקה עתידית": "next_inspection_date",
    "מספר טופס": "form_number",
    "סוג לקוח": "client_type",
  };
  let connection: mysql.Connection | undefined;

  try {
    // Create MySQL connection
    connection = await mysql.createConnection({
      host: "localhost",
      user: "root", // adjust as needed
      password: "rootpa", // adjust as needed
      database: "db_gasline",
    });

    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as (
      | string
      | number
    )[][];

    if (rows.length === 0) return;

    // Create table based on first row (headers)
    const headers = rows[0] as string[];
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS c005_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255),
        ${headers
          .map(
            (header) => `\`${columnMapping[header] || header}\` VARCHAR(255)`
          )
          .join(", ")}
      )
    `;

    await connection.execute(createTableSQL);
    console.log("Table c005_data created/verified");

    // Insert data rows (skip header row)
    const dataRows = rows.slice(1) as (string | number)[][];

    for (const row of dataRows) {
      const values = [fileName, ...headers.map((_, index) => {
        if (index < row.length) {
          const cellValue = row[index];
          return cellValue === null || cellValue === undefined
            ? null
            : String(cellValue);
        }
        return null;
      })];
      const placeholders = ['?', ...headers.map(() => "?")].join(", ");
      const englishHeaders = headers.map(header => columnMapping[header] || header);
      const columnNames = ['file_name', ...englishHeaders.map(h => `\`${h}\``)].join(", ");

      const insertSQL = `INSERT INTO c005_data (${columnNames}) VALUES (${placeholders})`;
      await connection.execute(insertSQL, values);
    }

    const client  = new Client();
    const inspection = new Inspection();

    client.id = UUID();

    console.log(`Inserted ${dataRows.length} rows into c005_data table`);
    return { success: true, rowsInserted: dataRows.length };
  } catch (error) {
    console.error("Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
