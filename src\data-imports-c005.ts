import * as XLSX from "xlsx";
import mysql from "mysql2/promise";
import { Client } from "./models/client";
import { Inspection } from "./models/inspection";
import { v4 as uuidv4 } from "uuid";
import { initFireBase, db } from "./firestart";

export async function readAndInsertExcelToMySQL(filePath: string) {
  // Extract filename without path and extension
  const fileName = filePath.split("\\").pop()?.split(".")[0] || "";

  // Hebrew to English column mapping
  const columnMapping: { [key: string]: string } = {
    "שם לקוח": "client_name",
    "מספר לקוח": "client_number",
    "תאריך בדיקה": "inspection_date",
    "תאריך בדיקה עתידית": "next_inspection_date",
    "מספר טופס": "form_number",
    "סוג לקוח": "client_type",
  };
  let connection: mysql.Connection | undefined;

  try {
    // Create MySQL connection
    connection = await mysql.createConnection({
      host: "localhost",
      user: "root", // adjust as needed
      password: "rootpa", // adjust as needed
      database: "gasline_db",
    });

    // Read the Excel file
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as (
      | string
      | number
    )[][];

    if (rows.length === 0) return;

    // Create table based on first row (headers)
    const headers = rows[0] as string[];
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS c005_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255),
        line_number INT,
        ${headers
          .map(
            (header) => `\`${columnMapping[header] || header}\` VARCHAR(255)`
          )
          .join(", ")}
      )
    `;

    await connection.execute(createTableSQL);
    console.log("Table c005_data created/verified");

    // Insert data rows (skip header row)
    const dataRows = rows.slice(1) as (string | number)[][];
    let index = 0;
    for (const row of dataRows) {
      const lineNumber = index + 2; // +2 because we skip header row and Excel rows are 1-indexed
      const values = [
        fileName,
        ...headers.map((_, index) => {
          if (index < row.length) {
            const cellValue = row[index];
            return cellValue === null || cellValue === undefined
              ? null
              : String(cellValue);
          }
          return null;
        }),
      ];
      const placeholders = ["?", ...headers.map(() => "?")].join(", ");
      const englishHeaders = headers.map(
        (header) => columnMapping[header] || header
      );
      const columnNames = [
        "file_name",
        ...englishHeaders.map((h) => `\`${h}\``),
      ].join(", ");

      const insertSQL = `INSERT INTO c005_data (${columnNames}) VALUES (${placeholders})`;
      await connection.execute(insertSQL, values);

      const client = new Client();
      const inspection = new Inspection();
      client.id = uuidv4();
      client.companyId = "C005";
      client.fullName = row[headers.indexOf("שם לקוח")] as string;
      client.clientNumInCompany = row[headers.indexOf("מספר לקוח")] as string;
      const clientType = row[headers.indexOf("סוג לקוח")] as string;
      client.notes = `[${fileName}:${lineNumber}] ${row
        .map((cell) => cell?.toString() || "")
        .join(" | ")}`;
      if (clientType.includes("פרטי") || clientType.includes("דיירים")) {
        client.businessalType = "private";
      } else if (clientType.includes("עסקי") || clientType.includes("תעשיה")) {
        client.businessalType = "business";
      } else {
        client.businessalType = "other";
      }

      if (client.clientNumInCompany.length > 7) {
        client.phone = client.clientNumInCompany;
      }

      inspection.id = uuidv4();
      inspection.clientId = client.id;
      inspection.businessalType = client.businessalType;
      inspection.isDetailed = false;

      inspection.inspectionDate = row[headers.indexOf("תאריך בדיקה")] as string;
      inspection.inspectionDate = inspection.inspectionDate
        .split("-")
        .reverse()
        .join("-");
      inspection.expirationDate = row[
        headers.indexOf("תאריך בדיקה עתידית")
      ] as string;
      inspection.expirationDate = inspection.expirationDate
        .split("-")
        .reverse()
        .join("-");
      inspection.inspectionNum = row[headers.indexOf("מספר טופס")] as string;
      inspection.clientName = client.fullName;
      inspection.companyId = "C005";
      inspection.clientIdNum = client.clientNumInCompany;
      inspection.passed = true;
      inspection.result = "p";
      inspection.status = "1";

      client.lastInspectionId = inspection.id;
      client.lastInspectionNum = inspection.inspectionNum;
      client.inspectionNum = inspection.inspectionNum;
      client.lastInspectionDate = inspection.inspectionDate;
      client.lastInspectionExpirationDate = inspection.expirationDate;

      await db
        .collection("clients")
        .doc(client.id)
        .set(JSON.parse(JSON.stringify(client)));
      await db
        .collection("inspections")
        .doc(inspection.id)
        .set(JSON.parse(JSON.stringify(inspection)));
      index++;
      // if (index == 5) break;
    }

    console.log(`Inserted ${dataRows.length} rows into c005_data table`);
    return { success: true, rowsInserted: dataRows.length };
  } catch (error) {
    console.error("Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
